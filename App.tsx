
import React, { useState, useEffect, useCallback } from 'react';
import type { Scene } from './types';
import { AppView } from './types';
import Header from './components/Header';
import StoryInputForm from './components/StoryInputForm';
import Storyboard from './components/Storyboard';
import PreviewPlayer from './components/PreviewPlayer';
import * as ttsService from './services/ttsService';

const App: React.FC = () => {
  const [storyTitle, setStoryTitle] = useState<string>('');
  const [scenes, setScenes] = useState<Scene[]>([]);
  const [currentView, setCurrentView] = useState<AppView>(AppView.INPUT_STORY_DETAILS);
  const [apiKey, setApiKey] = useState<string | undefined>(undefined);
  const [isLoadingApiKey, setIsLoadingApiKey] = useState(true);
  const [currentlyPlayingSceneId, setCurrentlyPlayingSceneId] = useState<string | null>(null);
  const [newSceneText, setNewSceneText] = useState<string>(''); // For adding new scene directly

  useEffect(() => {
    // In a real build process, process.env.API_KEY would be bundled.
    // For development or environments where it might be injected, this simulates readiness.
    const key = process.env.API_KEY;
    if (key) {
      setApiKey(key);
    } else {
      console.warn("API_KEY environment variable not found. AI features will be disabled.");
    }
    setIsLoadingApiKey(false);
  }, []);

  const handleStoryStart = (title: string, firstSceneText: string) => {
    setStoryTitle(title);
    const firstScene: Scene = {
      id: Date.now().toString(),
      text: firstSceneText,
      narrationText: firstSceneText,
      status: 'idle',
    };
    setScenes([firstScene]);
    setCurrentView(AppView.STORYBOARD);
  };

  const handleAddScene = () => {
    // A default new scene. User can edit it.
    const defaultNewSceneText = `Scene ${scenes.length + 1}: A new adventure begins...`;
    const newScene: Scene = {
      id: Date.now().toString(),
      text: newSceneText || defaultNewSceneText,
      narrationText: newSceneText || defaultNewSceneText,
      status: 'idle',
    };
    setScenes(prev => [...prev, newScene]);
    setNewSceneText(''); // Clear for next potential direct add.
  };
  
  const handleUpdateScene = useCallback((sceneId: string, updates: Partial<Scene>) => {
    setScenes(prevScenes =>
      prevScenes.map(s => (s.id === sceneId ? { ...s, ...updates } : s))
    );
  }, []);

  const handleDeleteScene = (sceneId: string) => {
    setScenes(prevScenes => prevScenes.filter(s => s.id !== sceneId));
  };

  const handleMoveScene = (sceneId: string, direction: 'up' | 'down') => {
    setScenes(prevScenes => {
      const scenesCopy = [...prevScenes];
      const index = scenesCopy.findIndex(s => s.id === sceneId);
      if (index === -1) return prevScenes;

      const targetIndex = direction === 'up' ? index - 1 : index + 1;
      if (targetIndex < 0 || targetIndex >= scenesCopy.length) return prevScenes;
      
      // Swap elements
      [scenesCopy[index], scenesCopy[targetIndex]] = [scenesCopy[targetIndex], scenesCopy[index]];
      return scenesCopy;
    });
  };

  const handleNarrationStart = (sceneId: string) => {
    setCurrentlyPlayingSceneId(sceneId);
    handleUpdateScene(sceneId, { status: 'playing_narration' });
  };
  
  const handleNarrationEnd = (sceneId: string) => {
    setCurrentlyPlayingSceneId(null);
    // Revert status to image_ready or idle if no image
    const scene = scenes.find(s => s.id === sceneId);
    if (scene) {
      handleUpdateScene(sceneId, { status: scene.imageUrl ? 'image_ready' : 'idle' });
    }
  };
  
  // Ensure any playing audio is stopped if view changes
  useEffect(() => {
    return () => {
      ttsService.cancelSpeech();
      setCurrentlyPlayingSceneId(null);
    };
  }, [currentView]);

  const navigateToHome = () => {
    setCurrentView(AppView.INPUT_STORY_DETAILS);
    // Optionally reset story state
    // setStoryTitle('');
    // setScenes([]);
  }

  const renderView = () => {
    if (isLoadingApiKey) {
      return <div className="flex justify-center items-center h-screen"><p>Loading Configuration...</p></div>;
    }

    switch (currentView) {
      case AppView.INPUT_STORY_DETAILS:
        return <StoryInputForm onStoryStart={handleStoryStart} />;
      case AppView.STORYBOARD:
        return (
          <Storyboard
            storyTitle={storyTitle}
            scenes={scenes}
            apiKey={apiKey}
            onUpdateScene={handleUpdateScene}
            onAddScene={handleAddScene}
            onDeleteScene={handleDeleteScene}
            onMoveScene={handleMoveScene}
            onPreviewStory={() => setCurrentView(AppView.PREVIEW_PLAYER)}
            currentlyPlayingSceneId={currentlyPlayingSceneId}
            onNarrationStart={handleNarrationStart}
            onNarrationEnd={handleNarrationEnd}
          />
        );
      case AppView.PREVIEW_PLAYER:
        return (
          <PreviewPlayer
            storyTitle={storyTitle}
            scenes={scenes}
            onClose={() => setCurrentView(AppView.STORYBOARD)}
          />
        );
      default:
        return <StoryInputForm onStoryStart={handleStoryStart} />;
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-slate-900">
      <Header onLogoClick={navigateToHome} />
      <main className="flex-grow py-8">
        {renderView()}
      </main>
      <footer className="bg-slate-800 text-center p-4 text-sm text-slate-500">
        © {new Date().getFullYear()} Story Animator AI. Powered by Imagination & Gemini.
      </footer>
    </div>
  );
};

export default App;
