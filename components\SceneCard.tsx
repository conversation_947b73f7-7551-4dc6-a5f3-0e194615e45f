
import React, { useState, useEffect, useCallback } from 'react';
import type { Scene } from '../types';
import { generateImageForScene } from '../services/geminiService';
import * as ttsService from '../services/ttsService';
import LoadingSpinner from './LoadingSpinner';
import { IconSparkles, IconPlay, IconPencil, IconTrash, IconPause } from '../constants';

interface SceneCardProps {
  scene: Scene;
  sceneIndex: number;
  totalScenes: number;
  apiKey: string | undefined;
  onUpdateScene: (sceneId: string, updates: Partial<Scene>) => void;
  onDeleteScene: (sceneId:string) => void;
  onMoveScene: (sceneId: string, direction: 'up' | 'down') => void;
  isCurrentlyPlayingNarration: boolean;
  onNarrationStart: (sceneId: string) => void;
  onNarrationEnd: (sceneId: string) => void;
}

const SceneCard: React.FC<SceneCardProps> = ({
  scene,
  sceneIndex,
  totalScenes,
  apiKey,
  onUpdateScene,
  onDeleteScene,
  onMoveScene,
  isCurrentlyPlayingNarration,
  onNarrationStart,
  onNarrationEnd,
}) => {
  const [isEditingText, setIsEditingText] = useState(false);
  const [editText, setEditText] = useState(scene.text);

  const handleGenerateImage = useCallback(async () => {
    if (!apiKey) {
      onUpdateScene(scene.id, { status: 'error_generating_image', errorMessage: 'API Key is not configured.' });
      return;
    }
    onUpdateScene(scene.id, { status: 'generating_image', errorMessage: undefined });
    try {
      // Consider using enhanceSceneDescriptionForImage here if desired
      const imageBase64 = await generateImageForScene(scene.text, apiKey);
      onUpdateScene(scene.id, {
        status: 'image_ready',
        imageUrl: `data:image/jpeg;base64,${imageBase64}`,
      });
    } catch (error) {
      console.error("Image generation failed for scene:", scene.id, error);
      onUpdateScene(scene.id, {
        status: 'error_generating_image',
        errorMessage: error instanceof Error ? error.message : 'Unknown error generating image.',
      });
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [apiKey, scene.id, scene.text, onUpdateScene]); // Do not add onUpdateScene to dependencies if it causes loops, ensure it's stable via useCallback in parent.

  const handleToggleNarration = () => {
    if (isCurrentlyPlayingNarration && scene.status === 'playing_narration') {
      ttsService.cancelSpeech();
      onNarrationEnd(scene.id); // This will set status to 'image_ready' or similar
    } else {
      ttsService.cancelSpeech(); // Stop any other speech
      onNarrationStart(scene.id);
      ttsService.speak(
        scene.narrationText,
        () => onNarrationEnd(scene.id),
        () => { // onError
          onUpdateScene(scene.id, { status: 'image_ready', errorMessage: 'TTS Error' });
          onNarrationEnd(scene.id);
        }
      );
    }
  };
  
  const handleSaveText = () => {
    onUpdateScene(scene.id, { text: editText, narrationText: editText }); // Update narration text too
    setIsEditingText(false);
  };

  // Cleanup speech on unmount or if scene changes significantly
  useEffect(() => {
    return () => {
      if (scene.status === 'playing_narration') {
         ttsService.cancelSpeech();
      }
    };
  }, [scene.status, scene.id]);

  const canMoveUp = sceneIndex > 0;
  const canMoveDown = sceneIndex < totalScenes - 1;

  return (
    <div className="bg-slate-800 rounded-xl shadow-xl overflow-hidden transition-all duration-300 ease-in-out hover:shadow-2xl_pink">
      <div className="p-1 bg-slate-700 text-xs text-slate-400 flex justify-between items-center">
        <span>Scene {sceneIndex + 1}</span>
        <div className="flex space-x-1">
          {canMoveUp && <button onClick={() => onMoveScene(scene.id, 'up')} className="hover:text-cyan-400 p-0.5 rounded">&#x25B2;</button>}
          {canMoveDown && <button onClick={() => onMoveScene(scene.id, 'down')} className="hover:text-cyan-400 p-0.5 rounded">&#x25BC;</button>}
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-5">
        {/* Image Column */}
        <div className="aspect-video bg-slate-700 rounded-lg flex items-center justify-center overflow-hidden relative">
          {scene.status === 'generating_image' && <LoadingSpinner text="Generating visual..." />}
          {scene.status === 'error_generating_image' && (
            <div className="text-center text-red-400 p-2">
              <p>Error generating image.</p>
              <p className="text-xs">{scene.errorMessage}</p>
              <button
                onClick={handleGenerateImage}
                className="mt-2 px-3 py-1 text-xs bg-cyan-600 hover:bg-cyan-500 text-white rounded"
              >
                Retry
              </button>
            </div>
          )}
          {scene.imageUrl && (scene.status === 'image_ready' || scene.status === 'playing_narration') && (
            <img src={scene.imageUrl} alt={`Visual for scene ${scene.id}`} className="w-full h-full object-cover" />
          )}
          {(!scene.imageUrl && scene.status === 'idle') && (
             <div className="text-center text-slate-400">
                <IconSparkles className="w-16 h-16 mx-auto text-slate-500 mb-2" />
                <p>Click "Generate Visual"</p>
             </div>
          )}
        </div>

        {/* Text and Controls Column */}
        <div className="flex flex-col justify-between space-y-3">
          {isEditingText ? (
            <div className="flex-grow">
              <textarea
                value={editText}
                onChange={(e) => setEditText(e.target.value)}
                className="w-full h-full p-2 bg-slate-700 border border-slate-600 rounded-md text-sm text-slate-100 focus:ring-1 focus:ring-cyan-500 outline-none resize-none"
                rows={5}
              />
            </div>
          ) : (
            <p className="text-sm text-slate-300 leading-relaxed flex-grow min-h-[80px] overflow-y-auto max-h-32 p-2 bg-slate-700/30 rounded">
              {scene.text}
            </p>
          )}

          <div className="space-y-2">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={handleGenerateImage}
                disabled={scene.status === 'generating_image' || !apiKey}
                title={!apiKey ? "API Key not configured" : "Generate visual for this scene"}
                className="flex-1 min-w-[120px] flex items-center justify-center px-3 py-2 text-xs sm:text-sm bg-pink-600 hover:bg-pink-500 text-white font-semibold rounded-md shadow-sm transition-colors disabled:opacity-60 disabled:cursor-not-allowed"
              >
                <IconSparkles className="w-4 h-4 mr-2" />
                {scene.imageUrl ? 'Regenerate' : 'Generate'} Visual
              </button>
              <button
                onClick={handleToggleNarration}
                disabled={scene.status === 'generating_image'}
                className="flex-1 min-w-[120px] flex items-center justify-center px-3 py-2 text-xs sm:text-sm bg-cyan-600 hover:bg-cyan-500 text-white font-semibold rounded-md shadow-sm transition-colors disabled:opacity-60"
              >
                {scene.status === 'playing_narration' ? <IconPause className="w-4 h-4 mr-2" /> : <IconPlay className="w-4 h-4 mr-2" />}
                {scene.status === 'playing_narration' ? 'Stop' : 'Narrate'}
              </button>
            </div>
            <div className="flex flex-wrap gap-2">
              {isEditingText ? (
                 <button
                    onClick={handleSaveText}
                    className="flex-1 min-w-[100px] px-3 py-1.5 text-xs bg-green-600 hover:bg-green-500 text-white rounded-md"
                  >
                    Save Text
                  </button>
              ) : (
                 <button
                    onClick={() => setIsEditingText(true)}
                    className="flex-1 min-w-[100px] flex items-center justify-center px-3 py-1.5 text-xs bg-slate-600 hover:bg-slate-500 text-slate-200 rounded-md"
                  >
                   <IconPencil className="w-3 h-3 mr-1.5" /> Edit Text
                  </button>
              )}
              <button
                onClick={() => onDeleteScene(scene.id)}
                className="flex-1 min-w-[100px] flex items-center justify-center px-3 py-1.5 text-xs bg-red-700 hover:bg-red-600 text-slate-200 rounded-md"
              >
                <IconTrash className="w-3 h-3 mr-1.5" /> Delete Scene
              </button>
            </div>
             {scene.status === 'error_generating_image' && scene.errorMessage && (
              <p className="text-xs text-red-400 mt-1">Error: {scene.errorMessage}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SceneCard;
