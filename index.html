
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Story Animator AI</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body {
      font-family: 'Inter', sans-serif; /* A clean, modern font */
    }
    /* Custom scrollbar for a more polished look */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #2d3748; /* slate-800 */
    }
    ::-webkit-scrollbar-thumb {
      background: #4a5568; /* slate-600 */
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #718096; /* slate-500 */
    }
  </style>
<script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "@google/genai": "https://esm.sh/@google/genai@^1.3.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-slate-900 text-slate-100 antialiased">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>
