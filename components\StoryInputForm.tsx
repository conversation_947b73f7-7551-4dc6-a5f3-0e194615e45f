
import React, { useState } from 'react';
import { PROMPT_SUGGESTIONS, IconPlusCircle, IconChevronRight } from '../constants';
import type { Scene } from '../types';

interface StoryInputFormProps {
  onStoryStart: (title: string, firstSceneText: string) => void;
}

const StoryInputForm: React.FC<StoryInputFormProps> = ({ onStoryStart }) => {
  const [title, setTitle] = useState<string>('');
  const [firstSceneText, setFirstSceneText] = useState<string>('');
  const [currentSuggestionIndex, setCurrentSuggestionIndex] = useState<number>(0);

  const handleStartStory = (e: React.FormEvent) => {
    e.preventDefault();
    if (title.trim() && firstSceneText.trim()) {
      onStoryStart(title.trim(), firstSceneText.trim());
    }
  };

  const handleUseSuggestion = () => {
    // A more elaborate suggestion could be: "A hero wakes up in a post-apocalyptic Lusaka..."
    // For now, using the general suggestions.
    const suggestion = PROMPT_SUGGESTIONS[currentSuggestionIndex];
    // Let's try to parse a title and scene from it.
    // This is a very basic parse.
    const parts = suggestion.split("...");
    if (parts.length > 0) {
      setTitle(parts[0].trim() || "My Story"); // Default title if suggestion is short
      setFirstSceneText(parts.slice(0).join("...").trim()); // Use the whole suggestion for first scene for simplicity
    } else {
      setFirstSceneText(suggestion);
    }
    setCurrentSuggestionIndex((prev) => (prev + 1) % PROMPT_SUGGESTIONS.length);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-slate-800 rounded-xl shadow-2xl space-y-6">
      <h2 className="text-3xl font-bold text-center text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-500 mb-6">
        Start Your Visual Story
      </h2>
      
      <form onSubmit={handleStartStory} className="space-y-6">
        <div>
          <label htmlFor="storyTitle" className="block text-sm font-medium text-slate-300 mb-1">
            Story Title
          </label>
          <input
            type="text"
            id="storyTitle"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="e.g., The Adventures of Sparky the Robot"
            className="w-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-slate-100 focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 outline-none transition-colors"
            required
          />
        </div>

        <div>
          <label htmlFor="firstSceneText" className="block text-sm font-medium text-slate-300 mb-1">
            Describe Your First Scene
          </label>
          <textarea
            id="firstSceneText"
            value={firstSceneText}
            onChange={(e) => setFirstSceneText(e.target.value)}
            rows={5}
            placeholder="e.g., In a sun-drenched meadow, a small, curious rabbit named Pip discovers a mysterious, shimmering portal..."
            className="w-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-slate-100 focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 outline-none transition-colors resize-none"
            required
          />
        </div>
        
        <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 sm:space-x-4">
          <button
            type="button"
            onClick={handleUseSuggestion}
            className="w-full sm:w-auto px-4 py-2 text-sm border border-pink-500 text-pink-400 rounded-lg hover:bg-pink-500 hover:text-white transition-colors flex items-center justify-center space-x-2"
          >
            <span>Try a Suggestion</span>
            <IconChevronRight className="w-4 h-4" />
          </button>
          <p className="text-xs text-slate-400 text-center sm:text-left italic h-8 overflow-hidden">
            "{PROMPT_SUGGESTIONS[currentSuggestionIndex]}"
          </p>
        </div>


        <button
          type="submit"
          disabled={!title.trim() || !firstSceneText.trim()}
          className="w-full flex items-center justify-center px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-semibold rounded-lg shadow-md hover:shadow-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <IconPlusCircle className="w-5 h-5 mr-2" />
          Create Story & Go to Storyboard
        </button>
      </form>
    </div>
  );
};

export default StoryInputForm;
