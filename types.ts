
export interface Scene {
  id: string;
  text: string;
  imagePromptSuggestion?: string;
  imageUrl?: string; // base64 data URI
  narrationText: string; // Text for TTS, initially same as scene.text
  status: 'idle' | 'generating_image' | 'image_ready' | 'error_generating_image' | 'playing_narration';
  errorMessage?: string;
}

export enum AppView {
  INPUT_STORY_DETAILS = 'INPUT_STORY_DETAILS', // For title and initial scene prompt
  STORYBOARD = 'STORYBOARD', // Main editing and scene generation view
  PREVIEW_PLAYER = 'PREVIEW_PLAYER', // Full story playback
}

export interface GeneratedImage {
  id: string; // Corresponds to scene ID
  base64: string;
}
