
import React from 'react';
import type { Scene } from '../types';
import SceneCard from './SceneCard';
import { IconPlusCircle, IconEye } from '../constants';

interface StoryboardProps {
  storyTitle: string;
  scenes: Scene[];
  apiKey: string | undefined;
  onUpdateScene: (sceneId: string, updates: Partial<Scene>) => void;
  onAddScene: () => void;
  onDeleteScene: (sceneId: string) => void;
  onMoveScene: (sceneId: string, direction: 'up' | 'down') => void;
  onPreviewStory: () => void;
  currentlyPlayingSceneId: string | null;
  onNarrationStart: (sceneId: string) => void;
  onNarrationEnd: (sceneId: string) => void;
}

const Storyboard: React.FC<StoryboardProps> = ({
  storyTitle,
  scenes,
  apiKey,
  onUpdateScene,
  onAddScene,
  onDeleteScene,
  onMoveScene,
  onPreviewStory,
  currentlyPlayingSceneId,
  onNarrationStart,
  onNarrationEnd,
}) => {
  if (!apiKey) {
    return (
      <div className="container mx-auto p-4 text-center">
        <p className="text-red-400 text-lg bg-red-900/50 p-4 rounded-md">
          API Key is not configured. Please set the <code className="font-mono bg-slate-700 p-1 rounded">process.env.API_KEY</code> environment variable.
          Image generation and other AI features will not work.
        </p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 space-y-8">
      <div className="text-center">
        <h2 className="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-pink-500 to-orange-400">
          {storyTitle}
        </h2>
        <p className="text-slate-400 mt-1">Craft your visual narrative scene by scene.</p>
      </div>

      {scenes.length === 0 && (
        <div className="text-center py-12">
          <p className="text-slate-500 text-lg mb-4">Your storyboard is empty. Let's add your first scene!</p>
        </div>
      )}

      <div className="space-y-6">
        {scenes.map((scene, index) => (
          <SceneCard
            key={scene.id}
            scene={scene}
            sceneIndex={index}
            totalScenes={scenes.length}
            apiKey={apiKey}
            onUpdateScene={onUpdateScene}
            onDeleteScene={onDeleteScene}
            onMoveScene={onMoveScene}
            isCurrentlyPlayingNarration={currentlyPlayingSceneId === scene.id}
            onNarrationStart={onNarrationStart}
            onNarrationEnd={onNarrationEnd}
          />
        ))}
      </div>

      <div className="flex flex-col sm:flex-row justify-center items-center gap-4 pt-6">
        <button
          onClick={onAddScene}
          className="w-full sm:w-auto flex items-center justify-center px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold rounded-lg shadow-md hover:shadow-lg transition-all"
        >
          <IconPlusCircle className="w-6 h-6 mr-2" />
          Add New Scene
        </button>
        {scenes.length > 0 && (
          <button
            onClick={onPreviewStory}
            className="w-full sm:w-auto flex items-center justify-center px-8 py-3 bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white font-semibold rounded-lg shadow-md hover:shadow-lg transition-all"
          >
            <IconEye className="w-6 h-6 mr-2" />
            Preview Full Story
          </button>
        )}
      </div>
    </div>
  );
};

export default Storyboard;
