
import React from 'react';
import { APP_TITLE, IconFilm } from '../constants';

interface HeaderProps {
  onLogoClick?: () => void;
}

const Header: React.FC<HeaderProps> = ({ onLogoClick }) => {
  // Simple logo: Typewriter + Film Strip elements
  const LogoIcon = () => (
    <div className="flex items-center space-x-1">
      <IconFilm className="w-8 h-8 text-cyan-400" />
       {/* A hint of a typewriter key */}
      <div className="w-5 h-5 bg-slate-700 rounded-sm shadow-md flex items-center justify-center text-xs font-mono text-cyan-300">T</div>
    </div>
  );


  return (
    <header className="bg-slate-800 shadow-lg p-4 sticky top-0 z-50">
      <div className="container mx-auto flex items-center justify-between">
        <div 
          className="flex items-center space-x-2 cursor-pointer group"
          onClick={onLogoClick}
        >
          <LogoIcon />
          <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-500 group-hover:from-cyan-300 group-hover:to-pink-400 transition-all">
            {APP_TITLE}
          </h1>
        </div>
        {/* Placeholder for future navigation or user actions */}
        <div>
          {/* <button className="text-slate-300 hover:text-cyan-400">Login</button> */}
        </div>
      </div>
    </header>
  );
};

export default Header;
