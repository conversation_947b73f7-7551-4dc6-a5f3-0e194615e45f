
import React, { useState, useEffect, useCallback } from 'react';
import type { Scene } from '../types';
import * as ttsService from '../services/ttsService';
import LoadingSpinner from './LoadingSpinner';
import { IconPlay, IconPause, IconChevronLeft, IconChevronRight, IconXMark } from '../constants';

interface PreviewPlayerProps {
  scenes: Scene[];
  storyTitle: string;
  onClose: () => void;
}

const PreviewPlayer: React.FC<PreviewPlayerProps> = ({ scenes, storyTitle, onClose }) => {
  const [currentSceneIndex, setCurrentSceneIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false); // Auto-play state
  const [isNarrationActive, setIsNarrationActive] = useState(false);

  const currentScene = scenes[currentSceneIndex];

  const playNarration = useCallback(() => {
    if (!currentScene) return;
    ttsService.cancelSpeech(); // Ensure any previous speech is stopped
    setIsNarrationActive(true);
    ttsService.speak(
      currentScene.narrationText,
      () => { // onEnd
        setIsNarrationActive(false);
        if (isPlaying && currentSceneIndex < scenes.length - 1) {
          setCurrentSceneIndex(prev => prev + 1);
        } else if (isPlaying && currentSceneIndex === scenes.length - 1) {
          setIsPlaying(false); // Stop autoplay at the end
        }
      },
      () => { // onError
        setIsNarrationActive(false);
        console.error("TTS Error during preview for scene: ", currentScene.id);
         if (isPlaying) setIsPlaying(false); // Stop autoplay on error
      }
    );
  }, [currentScene, isPlaying, currentSceneIndex, scenes.length]);

  useEffect(() => {
    if (isPlaying && currentScene) {
      playNarration();
    } else {
      ttsService.cancelSpeech();
      setIsNarrationActive(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isPlaying, currentScene]); // Re-run effect if isPlaying or currentScene changes

   // Cleanup speech on unmount
  useEffect(() => {
    return () => {
      ttsService.cancelSpeech();
    };
  }, []);


  const handleNextScene = () => {
    ttsService.cancelSpeech();
    setIsNarrationActive(false);
    if (currentSceneIndex < scenes.length - 1) {
      setCurrentSceneIndex(prev => prev + 1);
    } else { // Loop back or stop
      setCurrentSceneIndex(0); // Loop for now
      if (!isPlaying) setIsPlaying(true); // Start playing if manually cycled to beginning
    }
  };

  const handlePrevScene = () => {
    ttsService.cancelSpeech();
    setIsNarrationActive(false);
    if (currentSceneIndex > 0) {
      setCurrentSceneIndex(prev => prev - 1);
    } else { // Loop to end or stop
      setCurrentSceneIndex(scenes.length - 1); // Loop for now
      if (!isPlaying) setIsPlaying(true);
    }
  };

  const togglePlayPause = () => {
    setIsPlaying(prev => !prev);
  };

  if (!currentScene) {
    return (
      <div className="fixed inset-0 bg-slate-900 bg-opacity-95 flex flex-col items-center justify-center z-[100] p-4 text-slate-100">
        <p>No scenes to preview.</p>
        <button
          onClick={onClose}
          className="mt-4 px-4 py-2 bg-cyan-600 hover:bg-cyan-500 text-white rounded-lg"
        >
          Close Preview
        </button>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-slate-950 bg-opacity-90 backdrop-blur-md flex flex-col items-center justify-center z-[100] p-4 text-slate-100">
      <button
        onClick={onClose}
        className="absolute top-4 right-4 text-slate-400 hover:text-white transition-colors z-[101]"
        aria-label="Close Preview"
      >
        <IconXMark className="w-8 h-8" />
      </button>

      <div className="w-full max-w-3xl aspect-video bg-slate-800 rounded-xl shadow-2xl overflow-hidden relative">
        {currentScene.imageUrl ? (
          <img src={currentScene.imageUrl} alt={`Scene: ${currentScene.text.substring(0,30)}...`} className="w-full h-full object-contain" />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-slate-700">
            <LoadingSpinner text="Visual not generated or missing" />
          </div>
        )}
        {isNarrationActive && (
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 px-3 py-1 rounded-full text-xs animate-pulse">
            Narrating...
          </div>
        )}
      </div>

      <div className="w-full max-w-3xl mt-4 p-3 bg-slate-800/80 rounded-lg">
        <p className="text-sm text-center text-slate-300 italic line-clamp-2 min-h-[2.5em]">
          {currentScene.narrationText}
        </p>
      </div>
      
      <div className="mt-6 flex items-center space-x-4 md:space-x-6">
        <button onClick={handlePrevScene} className="p-2 rounded-full hover:bg-slate-700 transition-colors" aria-label="Previous Scene">
          <IconChevronLeft className="w-7 h-7 md:w-8 md:h-8 text-slate-300 hover:text-white" />
        </button>
        <button 
          onClick={togglePlayPause} 
          className="p-3 bg-cyan-600 hover:bg-cyan-500 rounded-full shadow-lg transition-transform hover:scale-105"
          aria-label={isPlaying ? "Pause Story" : "Play Story"}
        >
          {isPlaying ? <IconPause className="w-7 h-7 md:w-8 md:h-8 text-white" /> : <IconPlay className="w-7 h-7 md:w-8 md:h-8 text-white" />}
        </button>
        <button onClick={handleNextScene} className="p-2 rounded-full hover:bg-slate-700 transition-colors" aria-label="Next Scene">
          <IconChevronRight className="w-7 h-7 md:w-8 md:h-8 text-slate-300 hover:text-white" />
        </button>
      </div>
      <p className="mt-4 text-xs text-slate-500">Scene {currentSceneIndex + 1} of {scenes.length}</p>
      <p className="mt-1 text-lg font-semibold text-center">{storyTitle}</p>
    </div>
  );
};

export default PreviewPlayer;
