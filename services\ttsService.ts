
let currentUtterance: SpeechSynthesisUtterance | null = null;

export const speak = (text: string, onEnd?: () => void, onError?: (event: SpeechSynthesisErrorEvent) => void): void => {
  if (!window.speechSynthesis) {
    console.warn("Speech synthesis not supported by this browser.");
    if (onError) {
        // Simulate an error or handle gracefully
        const dummyUtterance = new SpeechSynthesisUtterance(''); // Create a dummy utterance
        const mockEventInit: SpeechSynthesisErrorEventInit = {
            utterance: dummyUtterance,
            error: 'synthesis-unavailable' // Use a valid SpeechSynthesisErrorCode
        };
        const mockEvent = new SpeechSynthesisErrorEvent('error', mockEventInit);
        onError(mockEvent);
    }
    return;
  }

  // Cancel any ongoing speech before starting a new one
  cancelSpeech();

  currentUtterance = new SpeechSynthesisUtterance(text);
  currentUtterance.lang = 'en-US'; // You can make this configurable

  currentUtterance.onend = () => {
    currentUtterance = null;
    if (onEnd) onEnd();
  };

  currentUtterance.onerror = (event: SpeechSynthesisErrorEvent) => {
    console.error("Speech synthesis error:", event.error);
    currentUtterance = null;
    if (onError) onError(event);
  };
  
  speechSynthesis.speak(currentUtterance);
};

export const cancelSpeech = (): void => {
  if (window.speechSynthesis && currentUtterance) {
    speechSynthesis.cancel();
    currentUtterance = null;
  } else if (window.speechSynthesis) {
    speechSynthesis.cancel(); // Cancel any other speech too
  }
};

export const isSpeaking = (): boolean => {
  return window.speechSynthesis ? window.speechSynthesis.speaking : false;
};
