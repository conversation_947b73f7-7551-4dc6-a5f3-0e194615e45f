
import { GoogleGenAI } from "@google/genai";
import type { GenerateContentResponse } from "@google/genai";

export const generateImageForScene = async (
  sceneText: string,
  apiKey: string
): Promise<string> => {
  if (!apiKey) {
    throw new Error("API Key is not configured.");
  }
  const ai = new GoogleGenAI({ apiKey });
  
  const styleVariations = ["cinematic and vibrant", "dreamlike and ethereal", "gritty and realistic", "cartoonish and playful", "dark fantasy style"];
  const randomStyle = styleVariations[Math.floor(Math.random() * styleVariations.length)];

  const prompt = `Create a ${randomStyle} image suitable for a story. The scene is: "${sceneText}". Ensure characters, actions, and setting described are visually prominent. If no specific characters are mentioned, create an evocative scene based on the text.`;

  try {
    const response = await ai.models.generateImages({
      model: 'imagen-3.0-generate-002',
      prompt: prompt,
      config: { numberOfImages: 1, outputMimeType: 'image/jpeg' },
    });

    if (response.generatedImages && response.generatedImages.length > 0 && response.generatedImages[0].image.imageBytes) {
      return response.generatedImages[0].image.imageBytes; // This is a base64 string
    } else {
      console.warn("No image generated or imageBytes missing:", response);
      throw new Error("No image was generated by the API, or the response was malformed.");
    }
  } catch (error) {
    console.error("Error generating image via Gemini API:", error);
    let errorMessage = "Unknown error during image generation.";
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    // Attempt to get a more specific message if it's a Gemini API error structure
    // This part is speculative as error structures can vary.
    const specificError = (error as any)?.message || (error as any)?.error?.message;
    if (specificError) {
      errorMessage = `Gemini API Error: ${specificError}`;
    }
    throw new Error(errorMessage);
  }
};

// Optional: A function to enhance scene text or extract keywords for better prompting
export const enhanceSceneDescriptionForImage = async (
  sceneText: string,
  apiKey: string
): Promise<string> => {
  if (!apiKey) {
    throw new Error("API Key is not configured.");
  }
  const ai = new GoogleGenAI({ apiKey });

  const prompt = `You are an assistant for a story visualization app. Given the following scene description, extract or rewrite it into a concise and visually descriptive prompt suitable for an image generation AI. Focus on key characters, actions, setting, and mood. Output only the refined prompt. Scene: "${sceneText}"`;
  
  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: 'gemini-2.5-flash-preview-04-17',
      contents: prompt,
    });
    return response.text.trim();
  } catch (error) {
    console.error("Error enhancing scene description:", error);
    // Fallback to original text if enhancement fails
    return sceneText; 
  }
};
